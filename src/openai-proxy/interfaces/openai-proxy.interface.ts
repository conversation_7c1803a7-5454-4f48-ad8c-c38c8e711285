import { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/index';
import { OpenAiResponseDto } from '../dto/openai-response.dto';

export interface OpenAiProxyServiceInterface {
  proxyToOpenAI(
    payload: {
      apiKey: string;
    } & ChatCompletionCreateParamsNonStreaming,
  ): Promise<OpenAiResponseDto>;
}

export const OPENAI_PROXY_SERVICE_TOKEN = Symbol('OpenAiProxyServiceInterface');
