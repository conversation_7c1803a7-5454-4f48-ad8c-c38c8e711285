import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ThrottlerGuard } from '@nestjs/throttler';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { OpenAiProxyService } from './openai-proxy.service';
import { OpenAiRequestDto } from './dto/openai-request.dto';
import { OpenAiResponseDto } from './dto/openai-response.dto';

@ApiTags('OpenAI Proxy')
@Controller('proxy/openai')
@UseGuards(ThrottlerGuard)
export class OpenAiProxyController {
  constructor(private readonly proxyService: OpenAiProxyService) {}

  @Post()
  @ApiOperation({
    summary: 'Proxy request to OpenAI Chat Completions API',
    description:
      'Forwards chat completion requests to OpenAI API with rate limiting and validation',
  })
  @ApiBody({
    type: OpenAiRequestDto,
    description:
      'OpenAI chat completion request payload with optional tools support',
    examples: {
      'basic-chat': {
        summary: 'Basic chat completion',
        value: {
          model: 'gpt-3.5-turbo',
          messages: [{ role: 'user', content: 'Hello, how are you?' }],
        },
      },
      'with-tools': {
        summary: 'Chat completion with function calling',
        value: {
          model: 'gpt-3.5-turbo',
          messages: [
            { role: 'system', content: 'You are a story generator.' },
            { role: 'user', content: 'Generate a fantasy story.' },
          ],
          tools: [
            {
              type: 'function',
              function: {
                name: 'submit_story',
                description: 'Submits the generated fantasy story.',
                parameters: {
                  type: 'object',
                  properties: {
                    title: {
                      type: 'string',
                      description:
                        'The catchy, appealing title of the story in Vietnamese.',
                    },
                    content: {
                      type: 'string',
                      description:
                        'The full story text in Vietnamese, formatted using simple Markdown.',
                    },
                  },
                  required: ['title', 'content'],
                },
              },
            },
          ],
          tool_choice: { type: 'function', function: { name: 'submit_story' } },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Successful response from OpenAI API',
    type: OpenAiResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid input data',
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async handle(@Body() payload: OpenAiRequestDto): Promise<OpenAiResponseDto> {
    return this.proxyService.proxyToOpenAI(payload);
  }
}
