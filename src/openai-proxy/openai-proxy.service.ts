import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import type { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/chat/completions';
import { ExternalServiceException } from '../common/exceptions';
import { CustomLoggerService } from '../common/logger/logger.service';
import { OpenAiResponseDto } from './dto/openai-response.dto';
import { OpenAiProxyServiceInterface } from './interfaces/openai-proxy.interface';

@Injectable()
export class OpenAiProxyService implements OpenAiProxyServiceInterface {
  private openai: OpenAI | undefined;

  constructor(private readonly logger: CustomLoggerService) {}

  async proxyToOpenAI({
    apiKey,
    ...payload
  }: {
    apiKey: string;
  } & ChatCompletionCreateParamsNonStreaming): Promise<OpenAiResponseDto> {
    this.logger.log('Proxying request to OpenAI', 'OpenAiProxyService');

    try {
      // Initialize OpenAI client
      this.openai = new OpenAI({ apiKey });

      const completion = await this.openai.chat.completions.create(payload);

      this.logger.log(
        'Successfully received response from OpenAI',
        'OpenAiProxyService',
      );

      return completion as OpenAiResponseDto;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to proxy request to OpenAI: ${errorMessage}`,
        errorStack,
        'OpenAiProxyService',
      );

      throw new ExternalServiceException(
        `OpenAI API request failed: ${errorMessage}`,
        'OpenAI',
      );
    }
  }
}
