import { Injectable } from '@nestjs/common';
import OpenAI from 'openai';
import type { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/chat/completions';
import { ExternalServiceException } from '../common/exceptions';
import { CustomLoggerService } from '../common/logger/logger.service';
import { OpenAiRequestDto } from './dto/openai-request.dto';
import { OpenAiResponseDto } from './dto/openai-response.dto';
import { OpenAiProxyServiceInterface } from './interfaces/openai-proxy.interface';

@Injectable()
export class OpenAiProxyService implements OpenAiProxyServiceInterface {
  private openai: OpenAI | undefined;

  constructor(private readonly logger: CustomLoggerService) {}

  async proxyToOpenAI(request: OpenAiRequestDto): Promise<OpenAiResponseDto> {
    this.logger.log('Proxying request to OpenAI', 'OpenAiProxyService');

    try {
      // Extract apiKey and create payload for OpenAI
      const { apiKey, ...requestData } = request;

      // Transform the request to match OpenAI SDK types
      const payload: ChatCompletionCreateParamsNonStreaming = {
        model: requestData.model,
        messages: requestData.messages as any, // Type assertion for compatibility
        ...(requestData.temperature !== undefined && {
          temperature: requestData.temperature,
        }),
        ...(requestData.max_tokens !== undefined && {
          max_tokens: requestData.max_tokens,
        }),
        ...(requestData.top_p !== undefined && { top_p: requestData.top_p }),
        ...(requestData.frequency_penalty !== undefined && {
          frequency_penalty: requestData.frequency_penalty,
        }),
        ...(requestData.presence_penalty !== undefined && {
          presence_penalty: requestData.presence_penalty,
        }),
        ...(requestData.logit_bias !== undefined && {
          logit_bias: requestData.logit_bias,
        }),
        ...(requestData.user !== undefined && { user: requestData.user }),
        ...(requestData.stop !== undefined && { stop: requestData.stop }),
        ...(requestData.tools !== undefined && {
          tools: requestData.tools as any,
        }),
        ...(requestData.tool_choice !== undefined && {
          tool_choice: requestData.tool_choice as any,
        }),
        ...(requestData.logprobs !== undefined && {
          logprobs: requestData.logprobs,
        }),
        ...(requestData.top_logprobs !== undefined && {
          top_logprobs: requestData.top_logprobs,
        }),
        ...(requestData.n !== undefined && { n: requestData.n }),
        ...(requestData.seed !== undefined && { seed: requestData.seed }),
        ...(requestData.response_format !== undefined && {
          response_format: requestData.response_format,
        }),
        stream: false, // Ensure non-streaming
      };

      // Initialize OpenAI client
      this.openai = new OpenAI({ apiKey });

      const completion = await this.openai.chat.completions.create(payload);

      this.logger.log(
        'Successfully received response from OpenAI',
        'OpenAiProxyService',
      );

      return completion as OpenAiResponseDto;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      this.logger.error(
        `Failed to proxy request to OpenAI: ${errorMessage}`,
        errorStack,
        'OpenAiProxyService',
      );

      throw new ExternalServiceException(
        `OpenAI API request failed: ${errorMessage}`,
        'OpenAI',
      );
    }
  }
}
