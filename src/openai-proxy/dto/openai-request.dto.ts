import {
  <PERSON><PERSON><PERSON>,
  <PERSON>Array,
  IsOptional,
  IsNumber,
  IsBoolean,
  ValidateNested,
  Min,
  Max,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ChatCompletionCreateParamsNonStreaming } from 'openai/resources/index';

export class MessageDto {
  @ApiProperty({
    description: 'The role of the message author',
    example: 'user',
    enum: ['system', 'user', 'assistant', 'function', 'tool'],
  })
  @IsString()
  role!: 'system' | 'user' | 'assistant' | 'function' | 'tool';

  @ApiProperty({
    description: 'The content of the message',
    example: 'Hello, how are you?',
  })
  @IsString()
  content!: string;

  @ApiPropertyOptional({
    description: 'An optional name for the participant',
    example: 'John',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Tool calls made by the assistant',
    type: 'array',
    items: { type: 'object' },
  })
  @IsOptional()
  @IsArray()
  tool_calls?: object[];

  @ApiPropertyOptional({
    description: 'Tool call ID for tool messages',
    example: 'call_abc123',
  })
  @IsOptional()
  @IsString()
  tool_call_id?: string;
}

export class FunctionParametersDto {
  @ApiProperty({
    description: 'The type of the parameters object',
    example: 'object',
  })
  @IsString()
  type!: string;

  @ApiProperty({
    description: 'The properties of the function parameters',
    example: {
      title: {
        type: 'string',
        description: 'The title of the story',
      },
    },
  })
  @IsObject()
  properties!: Record<string, unknown>;

  @ApiPropertyOptional({
    description: 'Required parameter names',
    type: [String],
    example: ['title', 'content'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  required?: string[];
}

export class FunctionDto {
  @ApiProperty({
    description: 'The name of the function',
    example: 'submit_story',
  })
  @IsString()
  name!: string;

  @ApiProperty({
    description: 'The description of the function',
    example: 'Submits the generated fantasy story',
  })
  @IsString()
  description!: string;

  @ApiProperty({
    description: 'The parameters schema for the function',
    type: FunctionParametersDto,
  })
  @ValidateNested()
  @Type(() => FunctionParametersDto)
  parameters!: FunctionParametersDto;
}

export class ToolDto {
  @ApiProperty({
    description: 'The type of the tool',
    example: 'function',
    enum: ['function'],
  })
  @IsString()
  type!: 'function';

  @ApiProperty({
    description: 'The function definition',
    type: FunctionDto,
  })
  @ValidateNested()
  @Type(() => FunctionDto)
  function!: FunctionDto;
}

export class ToolChoiceFunctionDto {
  @ApiProperty({
    description: 'The name of the function to call',
    example: 'submit_story',
  })
  @IsString()
  name!: string;
}

export class ToolChoiceObjectDto {
  @ApiProperty({
    description: 'The type of tool choice',
    example: 'function',
    enum: ['function'],
  })
  @IsString()
  type!: 'function';

  @ApiProperty({
    description: 'The specific function to call',
    type: ToolChoiceFunctionDto,
  })
  @ValidateNested()
  @Type(() => ToolChoiceFunctionDto)
  function!: ToolChoiceFunctionDto;
}

export class OpenAiRequestDto implements ChatCompletionCreateParamsNonStreaming {
  @ApiProperty({
    description: 'API key for OpenAI',
    example: 'your_openai_api_key_here',
  })
  @IsString()
  apiKey!: string;

  @ApiProperty({
    description: 'ID of the model to use',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  model!: string;

  @ApiProperty({
    description: 'A list of messages comprising the conversation so far',
    type: [MessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageDto)
  messages!: MessageDto[];

  @ApiPropertyOptional({
    description: 'What sampling temperature to use, between 0 and 2',
    minimum: 0,
    maximum: 2,
    example: 0.7,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({
    description: 'The maximum number of tokens to generate',
    minimum: 1,
    example: 150,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  max_tokens?: number;

  @ApiPropertyOptional({
    description: 'Nucleus sampling parameter',
    minimum: 0,
    maximum: 1,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  top_p?: number;

  @ApiPropertyOptional({
    description: 'Frequency penalty parameter',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  frequency_penalty?: number;

  @ApiPropertyOptional({
    description: 'Presence penalty parameter',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  presence_penalty?: number;

  @ApiPropertyOptional({
    description:
      'Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency',
    type: 'object',
    example: {},
  })
  @IsOptional()
  @IsObject()
  logit_bias?: Record<string, number>;

  @ApiPropertyOptional({
    description: 'A unique identifier representing your end-user',
    example: 'user-123',
  })
  @IsOptional()
  @IsString()
  user?: string;

  @ApiPropertyOptional({
    description:
      'Up to 4 sequences where the API will stop generating further tokens',
    type: [String],
    example: ['\n'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  stop?: string[] | string;

  @ApiPropertyOptional({
    description: 'Whether to stream back partial progress',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiPropertyOptional({
    description: 'A list of tools the model may call',
    type: [ToolDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ToolDto)
  tools?: ToolDto[];

  @ApiPropertyOptional({
    description: 'Controls which (if any) tool is called by the model',
    oneOf: [
      { type: 'string', enum: ['auto', 'none'] },
      { $ref: '#/components/schemas/ToolChoiceObjectDto' },
    ],
  })
  @IsOptional()
  tool_choice?: 'auto' | 'none' | ToolChoiceObjectDto;

  @ApiPropertyOptional({
    description: 'Whether to return log probabilities of the output tokens',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  logprobs?: boolean;

  @ApiPropertyOptional({
    description:
      'An integer between 0 and 20 specifying the number of most likely tokens to return at each token position',
    minimum: 0,
    maximum: 20,
    example: null,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(20)
  top_logprobs?: number;

  @ApiPropertyOptional({
    description:
      'Number of chat completion choices to generate for each input message',
    minimum: 1,
    maximum: 128,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(128)
  n?: number;

  @ApiPropertyOptional({
    description:
      'If specified, our system will make a best effort to sample deterministically',
    example: null,
  })
  @IsOptional()
  @IsNumber()
  seed?: number;

  @ApiPropertyOptional({
    description: 'Specifies the format that the model must output',
    type: 'object',
  })
  @IsOptional()
  @IsObject()
  response_format?: {
    type: 'text' | 'json_object';
  };
}
