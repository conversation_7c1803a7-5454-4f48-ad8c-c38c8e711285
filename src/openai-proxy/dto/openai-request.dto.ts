import {
  IsString,
  IsArray,
  IsOptional,
  IsNumber,
  IsBoolean,
  ValidateNested,
  Min,
  Max,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class MessageDto {
  @ApiProperty({
    description: 'The role of the message author',
    example: 'user',
    enum: ['system', 'user', 'assistant', 'function', 'tool'],
  })
  @IsString()
  role!: 'system' | 'user' | 'assistant' | 'function' | 'tool';

  @ApiProperty({
    description: 'The content of the message',
    example: 'Hello, how are you?',
  })
  @IsString()
  content!: string;
}

export class FunctionParametersDto {
  @ApiProperty({
    description: 'The type of the parameters object',
    example: 'object',
  })
  @IsString()
  type!: string;

  @ApiProperty({
    description: 'The properties of the function parameters',
    example: {
      title: {
        type: 'string',
        description: 'The title of the story',
      },
    },
  })
  @IsObject()
  properties!: Record<string, unknown>;

  @ApiPropertyOptional({
    description: 'Required parameter names',
    type: [String],
    example: ['title', 'content'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  required?: string[];
}

export class FunctionDto {
  @ApiProperty({
    description: 'The name of the function',
    example: 'submit_story',
  })
  @IsString()
  name!: string;

  @ApiProperty({
    description: 'The description of the function',
    example: 'Submits the generated fantasy story',
  })
  @IsString()
  description!: string;

  @ApiProperty({
    description: 'The parameters schema for the function',
    type: FunctionParametersDto,
  })
  @ValidateNested()
  @Type(() => FunctionParametersDto)
  parameters!: FunctionParametersDto;
}

export class ToolDto {
  @ApiProperty({
    description: 'The type of the tool',
    example: 'function',
    enum: ['function'],
  })
  @IsString()
  type!: 'function';

  @ApiProperty({
    description: 'The function definition',
    type: FunctionDto,
  })
  @ValidateNested()
  @Type(() => FunctionDto)
  function!: FunctionDto;
}

export class ToolChoiceFunctionDto {
  @ApiProperty({
    description: 'The name of the function to call',
    example: 'submit_story',
  })
  @IsString()
  name!: string;
}

export class ToolChoiceDto {
  @ApiProperty({
    description: 'The type of tool choice',
    example: 'function',
    enum: ['function', 'auto', 'none'],
  })
  @IsString()
  type!: 'function' | 'auto' | 'none';

  @ApiPropertyOptional({
    description: 'The specific function to call (when type is function)',
    type: ToolChoiceFunctionDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ToolChoiceFunctionDto)
  function?: ToolChoiceFunctionDto;
}

export class OpenAiRequestDto {
  @ApiProperty({
    description: 'Api key for OpenAI',
    example: 'your_openai_api_key_here',
  })
  @IsString()
  api_key!: string;

  @ApiProperty({
    description: 'ID of the model to use',
    example: 'gpt-3.5-turbo',
  })
  @IsString()
  model!: string;

  @ApiProperty({
    description: 'A list of messages comprising the conversation so far',
    type: [MessageDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageDto)
  messages!: MessageDto[];

  @ApiPropertyOptional({
    description: 'What sampling temperature to use, between 0 and 2',
    minimum: 0,
    maximum: 2,
    example: 0.7,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(2)
  temperature?: number;

  @ApiPropertyOptional({
    description: 'The maximum number of tokens to generate',
    minimum: 1,
    maximum: 4096,
    example: 150,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(4096)
  max_tokens?: number;

  @ApiPropertyOptional({
    description: 'Nucleus sampling parameter',
    minimum: -2,
    maximum: 2,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  top_p?: number;

  @ApiPropertyOptional({
    description: 'Frequency penalty parameter',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  frequency_penalty?: number;

  @ApiPropertyOptional({
    description: 'Presence penalty parameter',
    minimum: -2,
    maximum: 2,
    example: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(-2)
  @Max(2)
  presence_penalty?: number;

  @ApiPropertyOptional({
    description:
      'Up to 4 sequences where the API will stop generating further tokens',
    type: [String],
    example: ['\n'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  stop?: string[];

  @ApiPropertyOptional({
    description: 'Whether to stream back partial progress',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  stream?: boolean;

  @ApiPropertyOptional({
    description: 'A list of tools the model may call',
    type: [ToolDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ToolDto)
  tools?: ToolDto[];

  @ApiPropertyOptional({
    description: 'Controls which (if any) tool is called by the model',
    oneOf: [
      { type: 'string', enum: ['auto', 'none'] },
      { $ref: '#/components/schemas/ToolChoiceDto' },
    ],
  })
  @IsOptional()
  tool_choice?: 'auto' | 'none' | ToolChoiceDto;
}
